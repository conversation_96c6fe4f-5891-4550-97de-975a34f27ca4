import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';

import '../models/mood.dart';
import '../theme/app_theme.dart';

class ActivitiesPieChartWidget extends StatefulWidget {
  final List<MoodModel> moods;

  const ActivitiesPieChartWidget({super.key, required this.moods});

  @override
  State<ActivitiesPieChartWidget> createState() =>
      _ActivitiesPieChartWidgetState();
}

class _ActivitiesPieChartWidgetState extends State<ActivitiesPieChartWidget> {
  int touchedIndex = -1;

  @override
  Widget build(BuildContext context) {
    final activityData = _calculateActivityCounts();

    if (activityData.isEmpty) {
      return _buildEmptyState();
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppTheme.citrus.withValues(alpha: 0.1),
            AppTheme.appleCore.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppTheme.blueberry.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Activity Types',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppTheme.blueberry,
            ),
          ),
          const SizedBox(height: 16),
          SizedBox(
            height: 300,
            child: PieChart(
              PieChartData(
                pieTouchData: PieTouchData(
                  touchCallback: (FlTouchEvent event, pieTouchResponse) {
                    if (!event.isInterestedForInteractions ||
                        pieTouchResponse == null ||
                        pieTouchResponse.touchedSection == null) {
                      setState(() {
                        touchedIndex = -1;
                      });
                      return;
                    }

                    final newTouchedIndex =
                        pieTouchResponse.touchedSection!.touchedSectionIndex;

                    // Only show popup on tap up event to avoid double triggering
                    if (event is FlTapUpEvent &&
                        newTouchedIndex >= 0 &&
                        newTouchedIndex < activityData.length) {
                      _showActivityPopup(
                        context,
                        activityData[newTouchedIndex],
                      );
                    }

                    setState(() {
                      touchedIndex = newTouchedIndex;
                    });
                  },
                ),
                borderData: FlBorderData(show: false),
                sectionsSpace: 2,
                centerSpaceRadius: 60,
                sections: _buildPieChartSections(activityData),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Container(
      height: 300,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppTheme.citrus.withValues(alpha: 0.1),
            AppTheme.appleCore.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppTheme.blueberry.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.local_activity_outlined,
            size: 64,
            color: AppTheme.blueberry.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          Text(
            'No Activities Yet',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: AppTheme.blueberry.withValues(alpha: 0.7),
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Start adding activities to your moods\nto see your activity breakdown here!',
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppTheme.blueberry.withValues(alpha: 0.6),
            ),
          ),
        ],
      ),
    );
  }

  List<ActivityData> _calculateActivityCounts() {
    final Map<String, int> activityCounts = {};

    // Count activities from moods
    for (final mood in widget.moods) {
      if (mood.activity != null && mood.activity!.isNotEmpty) {
        activityCounts[mood.activity!] =
            (activityCounts[mood.activity!] ?? 0) + 1;
      }
    }

    // Convert to list and sort by count (descending)
    final activityList =
        activityCounts.entries
            .map(
              (entry) => ActivityData(
                name: entry.key,
                count: entry.value,
                color: _getActivityColor(entry.key),
              ),
            )
            .toList();

    activityList.sort((a, b) => b.count.compareTo(a.count));
    return activityList;
  }

  List<PieChartSectionData> _buildPieChartSections(
    List<ActivityData> activityData,
  ) {
    return activityData.asMap().entries.map((entry) {
      final index = entry.key;
      final data = entry.value;
      final isTouched = index == touchedIndex;
      final radius = isTouched ? 110.0 : 100.0;
      final fontSize = isTouched ? 18.0 : 16.0;

      return PieChartSectionData(
        color: data.color,
        value: data.count.toDouble(),
        title: data.count.toString(),
        radius: radius,
        titleStyle: TextStyle(
          fontSize: fontSize,
          fontWeight: FontWeight.bold,
          color: Colors.white,
          shadows: [
            Shadow(
              color: Colors.black.withValues(alpha: 0.7),
              offset: const Offset(1, 1),
              blurRadius: 2,
            ),
          ],
        ),
      );
    }).toList();
  }

  Color _getActivityColor(String activityName) {
    // Generate consistent colors for activities based on their name
    final colors = [
      AppTheme.blueberry,
      AppTheme.apricot,
      AppTheme.citrus,
      const Color(0xFF8E44AD), // Purple
      const Color(0xFF27AE60), // Green
      const Color(0xFFE74C3C), // Red
      const Color(0xFF3498DB), // Blue
      const Color(0xFFF39C12), // Orange
      const Color(0xFF9B59B6), // Violet
      const Color(0xFF1ABC9C), // Turquoise
      const Color(0xFFE67E22), // Carrot
      const Color(0xFF34495E), // Wet Asphalt
    ];

    final hash = activityName.hashCode;
    return colors[hash.abs() % colors.length];
  }

  void _showActivityPopup(BuildContext context, ActivityData activityData) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Container(
                width: 20,
                height: 20,
                decoration: BoxDecoration(
                  color: activityData.color,
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  activityData.name,
                  style: TextStyle(
                    color: AppTheme.blueberry,
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                  ),
                ),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Count: ${activityData.count}',
                style: TextStyle(
                  fontSize: 16,
                  color: AppTheme.blueberry.withValues(alpha: 0.8),
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'This activity appears in ${activityData.count} of your mood entries.',
                style: TextStyle(
                  fontSize: 14,
                  color: AppTheme.blueberry.withValues(alpha: 0.6),
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'Close',
                style: TextStyle(
                  color: AppTheme.blueberry,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}

class ActivityData {
  final String name;
  final int count;
  final Color color;

  ActivityData({required this.name, required this.count, required this.color});
}
